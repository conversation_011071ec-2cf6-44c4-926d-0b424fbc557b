import numpy
from pyscf import gto, scf, fci
import matplotlib.pyplot as plt
import time

# 1. 定义一系列的 H-H 键长 (单位：埃)
# From 0.5 to 1.0 Å with 0.02 Å spacing
R_HH_points_fine = numpy.arange(0.5, 1.02, 0.02)  # Includes 1.0

# From 1.05 to 2.0 Å with 0.05 Å spacing
R_HH_points_medium = numpy.arange(1.05, 2.05, 0.05) # Includes 2.0

# From 2.2 to 4.0 Å with 0.2 Å spacing for dissociation limit
R_HH_points_coarse = numpy.arange(2.2, 4.2, 0.2) # Includes 4.0

# Combine all points
bond_distances = numpy.concatenate([R_HH_points_fine, R_HH_points_medium, R_HH_points_coarse])
bond_distances = numpy.round(bond_distances, 3) # Round to avoid floating point display issues

print(f"将要计算的键长点 ({len(bond_distances)} 个):")
print(bond_distances)
print("-" * 50)

energies_s0 = []
energies_s1 = []

# 定义基组
basis_set = 'cc-pVTZ'

# 计算单个氢原子的能量作为参考 (使用与 H2 相同的基组)
mol_H = gto.M(atom='H 0 0 0', basis=basis_set, spin=1) # 氢原子是双重态 (spin=1 -> 2S+1=2)
mf_H_uhf = scf.UHF(mol_H).run(verbose=0)
energy_H_atom = mf_H_uhf.e_tot
print(f"单个氢原子的能量 (UHF/{basis_set}): {energy_H_atom:.8f} Ha")
dissociation_limit = 2 * energy_H_atom
print(f"H2 解离极限 (2 * E_H): {dissociation_limit:.8f} Ha")
print("-" * 50)

total_start_time = time.time()

for i, r in enumerate(bond_distances):
    loop_start_time = time.time()
    print(f"正在计算键长 ({i+1}/{len(bond_distances)}): {r:.3f} Å")
    # 2. 定义 H2 分子
    mol = gto.Mole()
    mol.atom = [
        ['H', (0, 0, 0)],
        ['H', (0, 0, r)]
    ]
    mol.basis = basis_set
    mol.spin = 0         # S=0, 单重态
    mol.symmetry = False # 关闭对称性以便于观察轨道（可选）
    mol.build(verbose=0) # verbose=0 for mol.build to suppress basis set info

    # 3. 进行 RHF 计算
    mf = scf.RHF(mol)
    mf.conv_tol = 1e-10 # 更严格的收敛标准
    mf.kernel(verbose=0) # verbose=0 关闭详细输出

    if not mf.converged:
        print(f"  警告: RHF 在键长 {r:.3f} Å 处未收敛!")
        # 可以选择跳过或者使用之前的能量作为近似等处理方式
        # 为简单起见，我们仍然尝试FCI，但结果可能不可靠
    
    # 4. 进行 FCI 计算
    cisolver = fci.FCI(mol, mf.mo_coeff)
    cisolver.nroots = 2  # 我们需要两个根（S0 和 S1）
    
    e_fci, c_fci = cisolver.kernel(verbose=0)

    energies_s0.append(e_fci[0])
    energies_s1.append(e_fci[1])

    loop_end_time = time.time()
    print(f"  S0 能量: {e_fci[0]:.8f} Ha")
    print(f"  S1 能量: {e_fci[1]:.8f} Ha")
    print(f"  此步用时: {loop_end_time - loop_start_time:.2f} 秒")
    print("-" * 30)

total_end_time = time.time()
print(f"\n所有计算完成！总用时: {total_end_time - total_start_time:.2f} 秒")

# 5. 绘制能量曲线
plt.figure(figsize=(12, 7)) # 稍微调整图形大小
plt.plot(bond_distances, energies_s0, 'o-', label=r'$S_0$ (FCI)')
plt.plot(bond_distances, energies_s1, 's-', label=r'$S_1$ (FCI)')

# 绘制解离极限参考线
plt.axhline(dissociation_limit, color='gray', linestyle='--', label=f'解离极限 (2 E(H) = {dissociation_limit:.6f} Ha)')

plt.xlabel(r'H-H 键长 ($\AA$)')
plt.ylabel('能量 (Hartree)')
plt.title(f'$H_2$ 解离曲线 (FCI/{basis_set})')
plt.legend()
plt.grid(True)
plt.ylim(min(energies_s0)-0.1, max(energies_s1)+0.1 if energies_s1 else dissociation_limit + 0.5) # 调整y轴范围
plt.tight_layout() # 自动调整子图参数，使其填充整个图像区域
plt.show()

# 找到基态能量最低点
min_energy_s0_idx = numpy.argmin(energies_s0)
min_energy_s0 = energies_s0[min_energy_s0_idx]
eq_bond_distance_s0 = bond_distances[min_energy_s0_idx]

print(f"\n基态 S0 在平衡点附近的能量: {min_energy_s0:.8f} Ha")
print(f"基态 S0 平衡键长 (近似): {eq_bond_distance_s0:.3f} Å")

# 如果S1有势阱，也可以找出来 (通常H2的S1是排斥的或非常浅的势阱)
min_energy_s1_idx = numpy.argmin(energies_s1)
min_energy_s1 = energies_s1[min_energy_s1_idx]
eq_bond_distance_s1 = bond_distances[min_energy_s1_idx]

# 检查 S1 是否真的有势阱（即能量低于解离极限且不是单调递减）
if min_energy_s1 < dissociation_limit and not all(numpy.diff(energies_s1[:min_energy_s1_idx+1]) < 0):
    print(f"激发态 S1 在平衡点附近的能量: {min_energy_s1:.8f} Ha")
    print(f"激发态 S1 平衡键长 (近似): {eq_bond_distance_s1:.3f} Å")
else:
    print("激发态 S1 似乎是排斥的或没有明显的势阱。")