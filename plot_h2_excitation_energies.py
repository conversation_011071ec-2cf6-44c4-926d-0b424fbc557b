#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
import matplotlib.pyplot as plt

# 定义键长点
R_HH_points = np.array([
    0.500, 0.520, 0.540, 0.560, 0.580, 0.600, 0.620, 0.640, 0.660, 0.680, 0.700, 0.720,
    0.740, 0.760, 0.780, 0.800, 0.820, 0.840, 0.860, 0.880, 0.900, 0.920, 0.940, 0.960,
    0.980, 1.000, 1.050, 1.100, 1.150, 1.200, 1.250, 1.300, 1.350, 1.400, 1.450, 1.500,
    1.550, 1.600, 1.650, 1.700, 1.750, 1.800, 1.850, 1.900, 1.950, 2.000, 2.200, 2.400,
    2.600, 2.800, 3.000, 3.200, 3.400, 3.600, 3.800, 4.000
])

# FCI数据
energies_S0_fci = np.array([
    -1.10086969, -1.11552294, -1.12782560, -1.13808850, -1.14657683, -1.15351794,
    -1.15910751, -1.16351462, -1.16688588, -1.16934879, -1.17101460, -1.17198060,
    -1.17233211, -1.17214413, -1.17148274, -1.17040626, -1.16896631, -1.16720858,
    -1.16517369, -1.16289771, -1.16041281, -1.15774767, -1.15492792, -1.15197651,
    -1.14891400, -1.14575885, -1.13757053, -1.12911991, -1.12057528, -1.11206758,
    -1.10369897, -1.09554906, -1.08767956, -1.08013772, -1.07295885, -1.06616837,
    -1.05978329, -1.05381343, -1.04826237, -1.04312824, -1.03840441, -1.03408016,
    -1.03014127, -1.02657065, -1.02334892, -1.02045501, -1.01170740, -1.00642097,
    -1.00336980, -1.00166287, -1.00072581, -1.00021697, -0.99994269, -0.99979571,
    -0.99971716, -0.99967505
])

energies_S1_fci = np.array([
    -0.57324583, -0.59936018, -0.62309183, -0.64474827, -0.66458976, -0.68283758,
    -0.69968069, -0.71528104, -0.72977787, -0.74329118, -0.75592458, -0.76776765,
    -0.77889800, -0.78938289, -0.79928073, -0.80864232, -0.81751192, -0.82592817,
    -0.83392494, -0.84153198, -0.84877555, -0.85567893, -0.86226285, -0.86854589,
    -0.87454478, -0.88027467, -0.89351041, -0.90533554, -0.91590565, -0.92535363,
    -0.93379539, -0.94133349, -0.94805950, -0.95405557, -0.95939570, -0.96414665,
    -0.96836876, -0.97211663, -0.97543969, -0.97838270, -0.98098614, -0.98328659,
    -0.98531705, -0.98710722, -0.98868384, -0.99007086, -0.99412056, -0.99649957,
    -0.99787796, -0.99866582, -0.99910877, -0.99935292, -0.99948475, -0.99955453,
    -0.99959066, -0.99960880
])

# CASSCF数据
energies_S0_casscf = np.array([
    -1.06168776, -1.07675988, -1.08949343, -1.10019988, -1.10914612, -1.11655857,
    -1.12263270, -1.12754070, -1.13143050, -1.13442500, -1.13663699, -1.13816298,
    -1.13908731, -1.13948367, -1.13941694, -1.13894287, -1.13811433, -1.13697364,
    -1.13556129, -1.13390917, -1.13205196, -1.13001342, -1.12782093, -1.12549624,
    -1.12305697, -1.12052339, -1.11387125, -1.10692683, -1.09985287, -1.09277591,
    -1.08579487, -1.07898666, -1.07241045, -1.06611108, -1.06012148, -1.05446470,
    -1.04915566, -1.04420194, -1.03960684, -1.03536460, -1.03147034, -1.02791254,
    -1.02467797, -1.02175072, -1.01911339, -1.01674700, -1.00960273, -1.00527574,
    -1.00275967, -1.00133750, -1.00054907, -1.00011787, -0.99988489, -0.99976051,
    -0.99969481, -0.99966022
])

energies_S1_casscf = np.array([
    -0.55919555, -0.58566073, -0.60973496, -0.63172548, -0.65189148, -0.67045587,
    -0.68760846, -0.70350856, -0.71829429, -0.73209023, -0.74499809, -0.75710765,
    -0.76849670, -0.77923285, -0.78937461, -0.79897417, -0.80807336, -0.81671307,
    -0.82492612, -0.83274543, -0.84019377, -0.84729848, -0.85407777, -0.86055073,
    -0.86673645, -0.87264776, -0.88632125, -0.89856249, -0.90952969, -0.91935828,
    -0.92816619, -0.93605757, -0.94312525, -0.94945218, -0.95511279, -0.96017395,
    -0.96469570, -0.96873247, -0.97233174, -0.97554035, -0.97839577, -0.98093477,
    -0.98318951, -0.98518943, -0.98696091, -0.98852821, -0.99315706, -0.99591780,
    -0.99753215, -0.99846021, -0.99898487, -0.99927676, -0.99943678, -0.99952351,
    -0.99957001, -0.99959470
])

# MSDFT-LYP数据
energies_S0_msdft = np.array([
    -1.11775169, -1.13266239, -1.14524103, -1.15579878, -1.16460257, -1.17187802,
    -1.17781997, -1.18260112, -1.18636931, -1.18788145, -1.19134254, -1.19343446,
    -1.19357100, -1.19385938, -1.19368607, -1.19310605, -1.19217274, -1.19092754,
    -1.18819146, -1.18768725, -1.18569314, -1.18354964, -1.18125233, -1.17788877,
    -1.17627810, -1.17363938, -1.16672373, -1.15736387, -1.15218347, -1.14485192,
    -1.13762298, -1.12994900, -1.12376906, -1.11725063, -1.11105369, -1.10520194,
    -1.09971069, -1.09458781, -1.08983614, -1.08672873, -1.08142543, -1.07773169,
    -1.07440642, -1.07138215, -1.06865766, -1.06621326, -1.05883215, -1.05435560,
    -1.05174422, -1.05026027, -1.04943074, -1.04897169, -1.04871956, -1.04858201,
    -1.04850733, -1.04846686
])

energies_S1_msdft = np.array([
    -0.61525948, -0.64156324, -0.66548256, -0.68732438, -0.70734793, -0.72577532,
    -0.74279573, -0.75856897, -0.77323310, -0.78554668, -0.79970364, -0.81237913,
    -0.82298038, -0.83360856, -0.84364374, -0.85313734, -0.86213177, -0.87066697,
    -0.87755629, -0.88652351, -0.89383494, -0.90083470, -0.90750917, -0.91294327,
    -0.91995758, -0.92576375, -0.93917373, -0.94899954, -0.96186029, -0.97143429,
    -0.97999430, -0.98701991, -0.99448386, -1.00059173, -1.00604500, -1.01091119,
    -1.01525073, -1.01911834, -1.02256105, -1.02690448, -1.02835086, -1.03075392,
    -1.03291795, -1.03482086, -1.03650518, -1.03799447, -1.04238648, -1.04499766,
    -1.04651671, -1.04738297, -1.04786654, -1.04813059, -1.04827145, -1.04834502,
    -1.04838252, -1.04840134
])

# 计算激发态能量 (S1-S0) 单位：eV
hartree_to_ev = 27.2114  # 1 Hartree = 27.2114 eV

# 计算激发态能量 (S1-S0)
excitation_energy_fci = (energies_S1_fci - energies_S0_fci) * hartree_to_ev
excitation_energy_casscf = (energies_S1_casscf - energies_S0_casscf) * hartree_to_ev
excitation_energy_msdft = (energies_S1_msdft - energies_S0_msdft) * hartree_to_ev

# 打印前几个点的激发态能量，用于调试
print("前5个点的激发态能量 (eV):")
print("R_HH (A) | FCI | SA-CASSCF | MSDFT-LYP")
for i in range(5):
    print(f"{R_HH_points[i]:.3f} | {excitation_energy_fci[i]:.4f} | {excitation_energy_casscf[i]:.4f} | {excitation_energy_msdft[i]:.4f}")

# 绘制激发态能量图
plt.figure(figsize=(14, 8))

plt.plot(R_HH_points, excitation_energy_fci, 'g-', label='FCI', linewidth=3)
plt.plot(R_HH_points, excitation_energy_casscf, 'bo-', label='SA-CASSCF', linewidth=2, markersize=6)
plt.plot(R_HH_points, excitation_energy_msdft, 'r*--', label='MSDFT-LYP', linewidth=2, markersize=8)

plt.xlabel('H-H Distance (Angstrom)', fontsize=14)
plt.ylabel('Excitation Energy (eV)', fontsize=14)
plt.title('H$_2$ Excitation Energy (S1-S0): FCI vs SA-CASSCF vs MSDFT-LYP', fontsize=16)
plt.legend(fontsize=12, loc='best')
plt.grid(True, linestyle=':', alpha=0.7)

# 设置y轴范围
y_min = min(np.min(excitation_energy_fci), np.min(excitation_energy_casscf), np.min(excitation_energy_msdft)) * 0.95
y_max = max(np.max(excitation_energy_fci), np.max(excitation_energy_casscf), np.max(excitation_energy_msdft)) * 1.05
plt.ylim(y_min, y_max)

plt.tight_layout()
plt.savefig("h2_excitation_energies.png", dpi=300)
print("\n激发态能量图表已保存为 h2_excitation_energies.png")

# 输出激发态能量表格
print("\n--- H2 激发态能量 (S1-S0) 结果 (eV) ---")
header = "R_HH (A) | FCI | SA-CASSCF | MSDFT-LYP"
print(header)
print("-" * len(header))

for i, r in enumerate(R_HH_points):
    print(f"{r:8.3f} | {excitation_energy_fci[i]:10.4f} | {excitation_energy_casscf[i]:10.4f} | {excitation_energy_msdft[i]:10.4f}")

# 计算与FCI的平均绝对误差 (MAE)
mae_casscf = np.mean(np.abs(excitation_energy_casscf - excitation_energy_fci))
mae_msdft = np.mean(np.abs(excitation_energy_msdft - excitation_energy_fci))

print("\n与FCI的平均绝对误差 (MAE):")
print(f"SA-CASSCF: {mae_casscf:.4f} eV")
print(f"MSDFT-LYP: {mae_msdft:.4f} eV")

# 计算与FCI的最大绝对误差
max_error_casscf = np.max(np.abs(excitation_energy_casscf - excitation_energy_fci))
max_error_msdft = np.max(np.abs(excitation_energy_msdft - excitation_energy_fci))

print("\n与FCI的最大绝对误差:")
print(f"SA-CASSCF: {max_error_casscf:.4f} eV")
print(f"MSDFT-LYP: {max_error_msdft:.4f} eV")

# 计算不同键长区域的平均绝对误差
# 平衡区域 (0.7-0.8 Å)
equilibrium_indices = np.where((R_HH_points >= 0.7) & (R_HH_points <= 0.8))[0]
mae_casscf_equilibrium = np.mean(np.abs(excitation_energy_casscf[equilibrium_indices] - excitation_energy_fci[equilibrium_indices]))
mae_msdft_equilibrium = np.mean(np.abs(excitation_energy_msdft[equilibrium_indices] - excitation_energy_fci[equilibrium_indices]))

# 解离区域 (2.0-4.0 Å)
dissociation_indices = np.where(R_HH_points >= 2.0)[0]
mae_casscf_dissociation = np.mean(np.abs(excitation_energy_casscf[dissociation_indices] - excitation_energy_fci[dissociation_indices]))
mae_msdft_dissociation = np.mean(np.abs(excitation_energy_msdft[dissociation_indices] - excitation_energy_fci[dissociation_indices]))

print("\n平衡区域 (0.7-0.8 Å) 的平均绝对误差:")
print(f"SA-CASSCF: {mae_casscf_equilibrium:.4f} eV")
print(f"MSDFT-LYP: {mae_msdft_equilibrium:.4f} eV")

print("\n解离区域 (2.0-4.0 Å) 的平均绝对误差:")
print(f"SA-CASSCF: {mae_casscf_dissociation:.4f} eV")
print(f"MSDFT-LYP: {mae_msdft_dissociation:.4f} eV")
