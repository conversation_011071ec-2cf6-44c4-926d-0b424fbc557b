#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy
from pyscf import gto, scf, mcscf, dft, lib, fci
from pyscf.tools import cubegen
import scipy.linalg
from scipy.spatial import KDTree
import multiprocessing
import time
import matplotlib.pyplot as plt

# --- Global Constants for LYP (from physrevb.37.785) ---
LYP_A = 0.04918
LYP_B = 0.132
LYP_C = 0.2533
LYP_D = 0.349
C_F = (3.0/10.0) * (3.0 * numpy.pi**2)**(2.0/3.0)
NUM_ELECTRONS_H2 = 2
N_STATES = 2 # S0 and S1

# --- Helper Function: Matrix Inverse Square Root ---
def calculate_matrix_inverse_square_root(S_matrix):
    """Calculates S^(-1/2) using eigenvalue decomposition."""
    try:
        eigvals, eigvecs = scipy.linalg.eigh(S_matrix)
        if numpy.any(eigvals <= 1e-12): # Threshold for positive definiteness
            print("Warning: Eigenvalue <= 0 found in S matrix during S^(-1/2) calculation.")
            if numpy.allclose(S_matrix, numpy.eye(S_matrix.shape[0])):
                return numpy.eye(S_matrix.shape[0])
            eigvals[eigvals <= 1e-12] = 1e-12

        inv_sqrt_eigvals = 1.0 / numpy.sqrt(eigvals)
        S_inv_sqrt = eigvecs @ numpy.diag(inv_sqrt_eigvals) @ eigvecs.T
        return S_inv_sqrt
    except Exception as e:
        print(f"Error in calculate_matrix_inverse_square_root: {e}")
        print(f"S_matrix was:\n{S_matrix}")
        raise

# --- Helper Function: Numerical Differentiation on Grid ---
def get_lambda_derivatives(lambda_values_at_k, k_index, all_lambda_values_for_state, grid_coords, kdtree, h_fd=1e-4):
    """
    Estimates gradient and Laplacian of lambda at grid_coords[k_index].
    lambda_values_at_k: scalar, lambda_j(r_k)
    k_index: integer, index of the current grid point
    all_lambda_values_for_state: 1D array of lambda_j values for all grid points for the current state j
    grid_coords: (Ngrid, 3) array of grid point coordinates
    kdtree: scipy.spatial.KDTree built on grid_coords
    h_fd: finite difference step (conceptual, actual step depends on neighbors)
    """
    grad_lambda = numpy.zeros(3)
    laplacian_lambda = 0.0
    current_pos = grid_coords[k_index]

    # Gradient (central difference idea)
    for i_dim in range(3): # x, y, z
        # Find neighbor in +h direction
        pos_plus_h = current_pos.copy()
        pos_plus_h[i_dim] += h_fd
        _, idx_plus = kdtree.query(pos_plus_h, k=1) # k=1 finds the closest point
        lambda_plus_h = all_lambda_values_for_state[idx_plus]
        actual_h_plus = grid_coords[idx_plus][i_dim] - current_pos[i_dim] # Actual step
        if abs(actual_h_plus) < 1e-7: actual_h_plus = h_fd # Avoid division by zero if same point

        # Find neighbor in -h direction
        pos_minus_h = current_pos.copy()
        pos_minus_h[i_dim] -= h_fd
        _, idx_minus = kdtree.query(pos_minus_h, k=1)
        lambda_minus_h = all_lambda_values_for_state[idx_minus]
        actual_h_minus = current_pos[i_dim] - grid_coords[idx_minus][i_dim] # Actual step
        if abs(actual_h_minus) < 1e-7: actual_h_minus = h_fd

        if abs(actual_h_plus + actual_h_minus) < 1e-7 : # Avoid if points are too close or on top
             grad_lambda[i_dim] = 0.0 # Or a more robust handling
        else:
             grad_lambda[i_dim] = (lambda_plus_h - lambda_minus_h) / (actual_h_plus + actual_h_minus)


    # Laplacian (sum of second derivatives d2f/dx2 + d2f/dy2 + d2f/dz2)
    # d2f/dx2 approx = 2 * (h1*f(x+h2) - (h1+h2)*f(x) + h2*f(x-h1)) / (h1*h2*(h1+h2))
    # where h1 is step to the left (actual_h_minus), h2 is step to the right (actual_h_plus)
    for i_dim in range(3):
        pos_plus_h = current_pos.copy(); pos_plus_h[i_dim] += h_fd
        _, idx_plus = kdtree.query(pos_plus_h, k=1)
        lambda_plus_h = all_lambda_values_for_state[idx_plus]
        h2 = grid_coords[idx_plus][i_dim] - current_pos[i_dim] # actual_h_plus for this dim
        if abs(h2) < 1e-7: h2 = h_fd


        pos_minus_h = current_pos.copy(); pos_minus_h[i_dim] -= h_fd
        _, idx_minus = kdtree.query(pos_minus_h, k=1)
        lambda_minus_h = all_lambda_values_for_state[idx_minus]
        h1 = current_pos[i_dim] - grid_coords[idx_minus][i_dim] # actual_h_minus for this dim
        if abs(h1) < 1e-7: h1 = h_fd

        denominator_lap = h1 * h2 * (h1 + h2)
        if abs(denominator_lap) > 1e-12: # Avoid division by zero
            numerator_lap = h1 * lambda_plus_h - (h1 + h2) * lambda_values_at_k + h2 * lambda_minus_h
            laplacian_lambda += 2 * numerator_lap / denominator_lap
        # else: laplacian term for this dimension is effectively zero or ill-defined

    return grad_lambda, laplacian_lambda

# --- Helper Function: LYP Correlation Energy Density g_LYP(lambda, grad_lambda, lap_lambda) ---
def calculate_gLYP_scalar(lambda_val, grad_lambda_vec, laplacian_lambda_val):
    """Calculates g_LYP for a given lambda and its derivatives."""
    if lambda_val < 1e-12: # Threshold for density being too small
        return 0.0

    grad_lambda_sq = numpy.dot(grad_lambda_vec, grad_lambda_vec)

    # Weizsacker kinetic energy density t_W(lambda)
    # t_W = (1/8) * |nabla lambda|^2 / lambda - (1/8) * nabla^2 lambda
    t_W_lambda = 0.0
    if lambda_val > 1e-9: # Avoid division by zero if lambda_val is too small
        t_W_lambda = (1.0/8.0) * (grad_lambda_sq / lambda_val) - (1.0/8.0) * laplacian_lambda_val
    elif abs(lambda_val) <= 1e-9 and abs(grad_lambda_sq) > 1e-9 : # lambda is zero but gradient is not
        # This case is problematic for t_W. LYP behavior at rho=0 with non-zero gradient needs careful consideration.
        # For simplicity here, we might set t_W to a value that doesn't cause explosion or use only the laplacian part.
        t_W_lambda = - (1.0/8.0) * laplacian_lambda_val # Only laplacian part if grad/lambda blows up
    else: # if lambda is very small, and gradient is also small
        t_W_lambda = - (1.0/8.0) * laplacian_lambda_val

    # LYP formula terms (from physrevb.37.785, Eq. 21)
    # g_LYP = -a * [rho + b*rho^(-2/3)*exp(-c*rho^(-1/3))*(CF*rho^(5/3) - 2*t_W + (1/9)*t_W + (1/18)*nabla^2_rho)] / (1+d*rho^(-1/3))
    # g_LYP = -a * [lambda + b*lambda^(-2/3)*exp(-c*lambda^(-1/3))*(CF*lambda^(5/3) - (17/9)*t_W + (1/18)*nabla^2_lambda)] / (1+d*lambda^(-1/3))

    # Handle terms involving powers of lambda carefully if lambda_val is very small
    if lambda_val < 1e-9: # If lambda is effectively zero, g_LYP should be zero
        return 0.0

    lambda_m13 = lambda_val**(-1.0/3.0)
    lambda_m23 = lambda_m13**2 # lambda_val**(-2.0/3.0)
    lambda_53  = lambda_val**(5.0/3.0)

    term_CF_lambda_53 = C_F * lambda_53
    term_tW_combo = -(17.0/9.0) * t_W_lambda
    term_laplacian_direct = (1.0/18.0) * laplacian_lambda_val # This is nabla^2_lambda, not t_W

    inner_bracket = term_CF_lambda_53 + term_tW_combo + term_laplacian_direct

    exp_term = numpy.exp(-LYP_C * lambda_m13)

    numerator = lambda_val + LYP_B * lambda_m23 * inner_bracket * exp_term
    denominator = 1.0 + LYP_D * lambda_m13

    if abs(denominator) < 1e-12:
        # This can happen if lambda_val is such that 1+d*lambda_m13 is zero.
        # print(f"Warning: Denominator in g_LYP is close to zero for lambda={lambda_val:.3e}. Returning 0.")
        return 0.0

    return -LYP_A * (numerator / denominator)

# --- Helper Function: Derivative of g_LYP w.r.t. lambda (g'_LYP) ---
def calculate_gLYP_scalar_derivative(lambda_val, grad_lambda_vec, laplacian_lambda_val):
    """Calculates dg_LYP/dlambda numerically, assuming grad_lambda and lap_lambda are fixed."""
    eps = 1e-6 # Finite difference step for lambda_val

    # Ensure lambda_val +/- eps doesn't go below zero if lambda_val is already small
    lambda_plus_eps = lambda_val + eps
    lambda_minus_eps = lambda_val - eps

    if lambda_minus_eps < 0: # If x-eps is negative, use one-sided difference or adjust
        if lambda_val < eps : # if x itself is very small
             # Forward difference: (g(x+eps) - g(x))/eps
             g_val_at_lambda = calculate_gLYP_scalar(lambda_val, grad_lambda_vec, laplacian_lambda_val)
             g_plus_eps = calculate_gLYP_scalar(lambda_plus_eps, grad_lambda_vec, laplacian_lambda_val)
             if abs(eps) < 1e-9: return 0.0 # Avoid division by zero if eps is too small
             return (g_plus_eps - g_val_at_lambda) / eps
        else: # lambda_val is > eps, but lambda_val - eps is negative. This shouldn't happen if eps is small.
              # Fallback to one-sided if lambda_minus_eps becomes problematic
             lambda_minus_eps = 1e-12 # A very small positive number


    g_plus_eps_val = calculate_gLYP_scalar(lambda_plus_eps, grad_lambda_vec, laplacian_lambda_val)
    g_minus_eps_val = calculate_gLYP_scalar(lambda_minus_eps, grad_lambda_vec, laplacian_lambda_val)

    denominator_deriv = lambda_plus_eps - lambda_minus_eps # Should be 2*eps
    if abs(denominator_deriv) < 1e-9: # Avoid division by zero
        return 0.0

    return (g_plus_eps_val - g_minus_eps_val) / denominator_deriv

# --- Worker function for parallel grid point processing ---
def process_grid_point(k_grid_point_idx, D_on_grid_at_k, S_matrix_global, S_inv_sqrt_matrix_global, S01_overlap_global,
                       all_lambda_values_state0_global, all_lambda_values_state1_global,
                       grid_coords_all_points, kdtree_all_points,
                       grid_weight_at_k):
    """
    Processes a single grid point to calculate its contribution to Ec_matrix.
    Returns G_AB(D(r_k)) * w_k (a 2x2 matrix)
    Changed argument names to avoid conflict with globals if this were not a pure function.
    """
    D_at_rk_local = D_on_grid_at_k

    # 4a. Calculate D_perp(r_k)
    # temp_matrix = S_inv_sqrt_matrix_global @ D_at_rk_local # Python 3.5+ for @
    temp_matrix = numpy.dot(S_inv_sqrt_matrix_global, D_at_rk_local)
    # D_perp_at_rk_local = temp_matrix @ S_inv_sqrt_matrix_global
    D_perp_at_rk_local = numpy.dot(temp_matrix, S_inv_sqrt_matrix_global)


    # 4b. Diagonalize D_perp(r_k)
    try:
        # eigenvalues_D_perp_at_rk are [lambda_0(r_k), lambda_1(r_k)]
        eigenvalues_D_perp_at_rk, _ = scipy.linalg.eigh(D_perp_at_rk_local)
    except scipy.linalg.LinAlgError:
        # This can happen if D_perp_at_rk is ill-conditioned or contains NaNs/Infs
        print(f"Warning: LinAlgError during diagonalization of D_perp at grid point index {k_grid_point_idx}. D_perp_at_rk_local:\n{D_perp_at_rk_local}")
        return numpy.zeros((N_STATES, N_STATES)) # Return zero contribution for this point

    lambda_0_rk_local = eigenvalues_D_perp_at_rk[0]
    lambda_1_rk_local = eigenvalues_D_perp_at_rk[1]

    # 4c. Numerical Derivatives of lambda_j(r_k)
    grad_lambda_0_rk, laplacian_lambda_0_rk = get_lambda_derivatives(
        lambda_0_rk_local, k_grid_point_idx, all_lambda_values_state0_global,
        grid_coords_all_points, kdtree_all_points
    )
    grad_lambda_1_rk, laplacian_lambda_1_rk = get_lambda_derivatives(
        lambda_1_rk_local, k_grid_point_idx, all_lambda_values_state1_global,
        grid_coords_all_points, kdtree_all_points
    )

    # 4d. Calculate g_LYP(lambda_j(r_k))
    g_val_lambda_0_rk = calculate_gLYP_scalar(lambda_0_rk_local, grad_lambda_0_rk, laplacian_lambda_0_rk)
    g_val_lambda_1_rk = calculate_gLYP_scalar(lambda_1_rk_local, grad_lambda_1_rk, laplacian_lambda_1_rk)

    # 4e. Calculate v_MS_c(r_k) and g_V(r_k)
    v_MS_c_rk_local = 0.0
    if abs(lambda_0_rk_local - lambda_1_rk_local) < 1e-9: # Threshold for degeneracy
        g_prime_val_lambda_0_rk = calculate_gLYP_scalar_derivative(lambda_0_rk_local, grad_lambda_0_rk, laplacian_lambda_0_rk)
        v_MS_c_rk_local = g_prime_val_lambda_0_rk
    else:
        v_MS_c_rk_local = (g_val_lambda_0_rk - g_val_lambda_1_rk) / (lambda_0_rk_local - lambda_1_rk_local)

    g_V_rk_local = 0.5 * (g_val_lambda_0_rk + g_val_lambda_1_rk)

    # 4f. Calculate Ensemble Density rho_V(r_k) (pnas_si Eq.49)
    # D_at_rk_local[0,1] is D01, D_at_rk_local[1,0] is D10. For real, they are same.
    D01_avg_rk_local = (D_at_rk_local[0,1] + D_at_rk_local[1,0]) * 0.5
    denominator_rho_V = 2.0 * (1.0 - S01_overlap_global**2)

    rho_V_rk_local = 0.0
    if abs(denominator_rho_V) < 1e-12:
        # This case implies S01_overlap is close to 1 (states are nearly linearly dependent)
        # or if S_matrix was not identity and 1-S01^2 is small.
        # For SA-CASSCF states, S01_overlap is 0, so denominator is 2.0.
        # This branch should ideally not be hit if S_matrix is identity.
        # print(f"Warning: Denominator for rho_V is close to zero at grid point {k_grid_point_idx}. S01={S01_overlap_global}")
        rho_V_rk_local = (D_at_rk_local[0,0] + D_at_rk_local[1,1]) / 2.0 # Fallback for safety
    else:
        rho_V_rk_local = (D_at_rk_local[0,0] + D_at_rk_local[1,1] - 2.0 * S01_overlap_global * D01_avg_rk_local) / denominator_rho_V

    # 4g. Calculate G_corr_density_matrix element G_AB(D(r_k)) * w_k
    # G_matrix = v_MS_c_rk * (D_at_rk - S_matrix * rho_V_rk) + S_matrix * g_V_rk

    # temp_S_rhoV_matrix = S_matrix_global * rho_V_rk_local -> This is element-wise if S_matrix is numpy array.
    # If S_matrix_global is a numpy.ndarray, then S_matrix_global * scalar is correct.
    temp_S_rhoV_matrix = S_matrix_global * rho_V_rk_local
    temp_D_minus_S_rhoV = D_at_rk_local - temp_S_rhoV_matrix

    term1_G = temp_D_minus_S_rhoV * v_MS_c_rk_local
    term2_G = S_matrix_global * g_V_rk_local

    G_matrix_at_rk_times_weight = (term1_G + term2_G) * grid_weight_at_k
    return G_matrix_at_rk_times_weight

# --- Main MSDFT Single Point Calculation ---
def calculate_msdft_lyp_single_point(R_HH_current_distance, mol_obj=None, mf_rhf=None, mc_sacasscf_obj=None):
    """Calculates MSDFT-LYP S0 and S1 energies for H2 at a given bond distance."""

    print(f"\n--- Starting MSDFT-LYP for R_HH = {R_HH_current_distance:.3f} A ---")

    # 1. Define Molecule and Perform SA-CASSCF
    if mol_obj is None or mc_sacasscf_obj is None: # Allow passing pre-computed CASSCF
        current_mol = gto.Mole()
        current_mol.atom = [['H', (0, 0, 0)], ['H', (0, 0, R_HH_current_distance)]]
        current_mol.basis = 'cc-pVTZ'
        current_mol.symmetry = False # For simplicity in this script
        current_mol.build()

        current_mf = scf.RHF(current_mol)
        current_mf.kernel(verbose=lib.logger.QUIET) # Suppress RHF output

        # Run Full CI calculation for reference
        cisolver = fci.FCI(current_mf)
        cisolver.nroots = N_STATES  # Calculate the same number of states as CASSCF
        fci_e, fci_civecs = cisolver.kernel(nroots=N_STATES)
        fci_S0_energy = fci_e[0] + current_mol.energy_nuc()  # Add nuclear repulsion
        fci_S1_energy = fci_e[1] + current_mol.energy_nuc()
        print(f"  FCI Energies: S0={fci_S0_energy:.8f}, S1={fci_S1_energy:.8f} Hartree")

        ncas, nelecas = 2, (1,1) # For H2
        current_mc = mcscf.CASSCF(current_mf, ncas, nelecas)
        current_mc.state_average_([0.5, 0.5]) # Equal weights for S0 and S1
        current_mc.nroots = N_STATES

        # Set verbosity for mc object before kernel call
        if R_HH_current_distance > 0.6: # Example condition from original error context
             current_mc.verbose = lib.logger.WARNING # PySCF's QUIET is effectively a high level like WARNING or ERROR
        else:
             current_mc.verbose = lib.logger.INFO # Standard info level
        current_mc.kernel() # Run CASSCF
    else:
        current_mol = mol_obj
        # current_mf = mf_rhf # Not strictly needed if mc_sacasscf_obj is passed and converged
        current_mc = mc_sacasscf_obj

    if not current_mc.converged:
        print(f"SA-CASSCF calculation did not converge for R_HH = {R_HH_current_distance:.3f} A.")
        return float('nan'), float('nan')

    # SA-CASSCF energies (already include nuclear repulsion)
    E_S0_cas, E_S1_cas = current_mc.e_states[0], current_mc.e_states[1]
    print(f"  SA-CASSCF Energies: S0={E_S0_cas:.8f}, S1={E_S1_cas:.8f} Hartree")

    # 2. Get SA-CASSCF Data for MSDFT
    # For SA-CASSCF eigenstates, S_AB = delta_AB
    S_matrix = numpy.eye(N_STATES)
    S_inv_sqrt_matrix = numpy.eye(N_STATES) # S^(-1/2) is also identity
    S01_overlap = S_matrix[0,1] # This will be 0.0

    # H_CASSCF_matrix has SA-CASSCF total energies on diagonal
    H_CASSCF_matrix = numpy.diag(current_mc.e_states)

    # Get 1-RDMs and 1-TDMs in AO basis
    dm_S0_ao = current_mc.make_rdm1(state=0)
    dm_S1_ao = current_mc.make_rdm1(state=1)

    # Get MO coefficients first (needed for all transformations)
    mo_coeff = current_mc.mo_coeff
    mo_cas = mo_coeff[:, current_mc.ncore:current_mc.ncore+current_mc.ncas]

    # Print state density matrices in CAS space
    print("\n--- Density Matrices in CAS Space ---")
    print(f"CAS space dimension: {current_mc.ncas}x{current_mc.ncas}")
    print(f"Number of electrons in CAS: {current_mc.nelecas}")

    # Transform AO density matrices to CAS space
    # D_cas = C_cas^T * D_ao * C_cas
    dm_S0_cas = numpy.dot(mo_cas.T, numpy.dot(dm_S0_ao, mo_cas))
    dm_S1_cas = numpy.dot(mo_cas.T, numpy.dot(dm_S1_ao, mo_cas))

    print("S0 Density Matrix (transformed from AO to CAS space):")
    print(dm_S0_cas)
    print("\nS1 Density Matrix (transformed from AO to CAS space):")
    print(dm_S1_cas)

    # For H2 with (2,2) active space, we can construct the transition density matrix manually
    print("\n--- Transition Density Matrix ---")

    # For H2 with (2,2) active space, construct a simple transition density matrix
    dm_S0_S1_cas = numpy.zeros((current_mc.ncas, current_mc.ncas))
    if current_mc.ncas == 2:  # For H2 with (2,2) active space
        # For singlet->triplet transition in H2, the TDM has a simple form
        dm_S0_S1_cas[0, 1] = 1.0/numpy.sqrt(2.0)  # Normalized value
        dm_S0_S1_cas[1, 0] = 1.0/numpy.sqrt(2.0)  # Symmetric for real orbitals

    print("Transition Density Matrix S0->S1 (CAS space):")
    print(dm_S0_S1_cas)

    # Transform to AO basis
    dm_S0_S1_ao = numpy.dot(mo_cas, numpy.dot(dm_S0_S1_cas, mo_cas.T))

    print("\nTransition Density Matrix S0->S1 (AO basis):")
    if dm_S0_S1_ao.shape[0] <= 10:
        print(dm_S0_S1_ao)
    else:
        print("Top-left 5x5 section:")
        print(dm_S0_S1_ao[:5,:5])

    # Print AO basis density matrices (truncated if large)
    print("\n--- Density Matrices in AO Basis ---")
    print(f"AO basis dimension: {dm_S0_ao.shape[0]}x{dm_S0_ao.shape[1]}")

    # Only print full matrices if they're small
    if dm_S0_ao.shape[0] <= 10:
        print("S0 Density Matrix (AO basis):")
        print(dm_S0_ao)
        print("\nS1 Density Matrix (AO basis):")
        print(dm_S1_ao)
        print("\nTransition Density Matrix S0->S1 (AO basis):")
        print(dm_S0_S1_ao)
    else:
        # Otherwise just print a small section
        print("S0 Density Matrix (AO basis, top-left 5x5 section):")
        print(dm_S0_ao[:5,:5])
        print("\nS1 Density Matrix (AO basis, top-left 5x5 section):")
        print(dm_S1_ao[:5,:5])
        print("\nTransition Density Matrix S0->S1 (AO basis, top-left 5x5 section):")
        print(dm_S0_S1_ao[:5,:5])

    # Setup DFT Grids
    grids = dft.gen_grid.Grids(current_mol)
    grids.atom_grid = (99, 590) # Higher precision grid (Levin-Laidlaw: (radial, angular))
    # Use available radial grid method
    grids.radi_method = dft.radi.treutler # Use available method
    grids.becke_scheme = dft.gen_grid.original_becke # Becke partitioning for multicenter integration
    grids.build(with_non0tab=True) # with_non0tab is important for eval_rho
    grid_coords = grids.coords
    grid_weights = grids.weights
    n_grid_points = grid_coords.shape[0]
    print(f"  Number of DFT grid points: {n_grid_points}")

    # Precompute D_AB(r_k) on the grid
    D_on_grid = numpy.zeros((n_grid_points, N_STATES, N_STATES))

    # Create a NumInt object for evaluating densities
    ni = dft.numint.NumInt()

    # Evaluate AO values on the grid first
    ao_values = ni.eval_ao(current_mol, grid_coords, deriv=0)

    # Calculate densities on grid using manual matrix multiplication
    # For each grid point, calculate ρ(r) = ∑_μν ao_μ(r) * D_μν * ao_ν(r)
    ngrids = grid_coords.shape[0]

    # For diagonal elements (state densities)
    for i in range(ngrids):
        ao_i = ao_values[i]
        # State 0 density
        D_on_grid[i, 0, 0] = numpy.dot(ao_i, numpy.dot(dm_S0_ao, ao_i))
        # State 1 density
        D_on_grid[i, 1, 1] = numpy.dot(ao_i, numpy.dot(dm_S1_ao, ao_i))
        # Transition density S0->S1
        D_on_grid[i, 0, 1] = numpy.dot(ao_i, numpy.dot(dm_S0_S1_ao, ao_i))
        # For real wavefunctions, D_10 = D_01
        D_on_grid[i, 1, 0] = D_on_grid[i, 0, 1]

    print("  Finished calculating densities on grid.")

    # 3. Calculate lambda_j(r_k) for all grid points (needed for numerical derivatives)
    print("  Pre-calculating lambda_j(r_k) for all grid points...")
    all_lambda_values_state0_on_grid = numpy.zeros(n_grid_points)
    all_lambda_values_state1_on_grid = numpy.zeros(n_grid_points)

    # Store eigenvectors for a sample grid point
    sample_eigvecs = None
    sample_point_idx = min(100, n_grid_points-1)  # Choose a reasonable sample point

    for k_idx_lambda_precalc in range(n_grid_points):
        D_at_rk_for_lambda = D_on_grid[k_idx_lambda_precalc]
        # Since S_inv_sqrt_matrix is identity, D_perp_at_rk = D_at_rk_for_lambda
        D_perp_at_rk_for_lambda = D_at_rk_for_lambda

        try:
            # Get both eigenvalues and eigenvectors
            eigvals_k_lambda, eigvecs_k_lambda = scipy.linalg.eigh(D_perp_at_rk_for_lambda)

            # Store sample eigenvectors
            if k_idx_lambda_precalc == sample_point_idx:
                sample_eigvecs = eigvecs_k_lambda

        except scipy.linalg.LinAlgError:
             # Fallback if diagonalization fails for some reason
             print(f"Warning: LinAlgError during pre-calculation of lambda at grid point {k_idx_lambda_precalc}. D_perp:\n{D_perp_at_rk_for_lambda}")
             eigvals_k_lambda = numpy.array([0.0, 0.0]) # Avoid NaN, use zero

        all_lambda_values_state0_on_grid[k_idx_lambda_precalc] = eigvals_k_lambda[0]
        all_lambda_values_state1_on_grid[k_idx_lambda_precalc] = eigvals_k_lambda[1]

    print("  Finished pre-calculating lambda_j(r_k).")

    # Print sample density matrix and its diagonalization at a grid point
    print("\n--- Sample Density Matrix at Grid Point ---")
    sample_D = D_on_grid[sample_point_idx]
    print(f"Grid point index: {sample_point_idx}")
    print("Density matrix D(r_k):")
    print(sample_D)

    print("\nEigenvalues (lambda_j):")
    print(f"lambda_0 = {all_lambda_values_state0_on_grid[sample_point_idx]}")
    print(f"lambda_1 = {all_lambda_values_state1_on_grid[sample_point_idx]}")

    if sample_eigvecs is not None:
        print("\nEigenvectors (columns):")
        print(sample_eigvecs)

        # Verify diagonalization
        print("\nVerification of diagonalization:")
        diag_D = numpy.dot(sample_eigvecs.T, numpy.dot(sample_D, sample_eigvecs))
        print("Diagonalized D matrix:")
        print(diag_D)

    # Build KDTree for nearest neighbor search in numerical differentiation
    kdtree_grid = KDTree(grid_coords)

    # 4. Calculate Correlation Energy Matrix Ec_matrix (Parallelized)
    # Prepare arguments for each grid point processing
    task_args = []
    for k_task_idx in range(n_grid_points):
        task_args.append((
            k_task_idx, D_on_grid[k_task_idx], S_matrix, S_inv_sqrt_matrix, S01_overlap,
            all_lambda_values_state0_on_grid, all_lambda_values_state1_on_grid,
            , kdtree_grid, grid_weights[k_task_idx]
        ))

    print(f"  Starting parallel computation of Ec contributions over {n_grid_points} grid points...")
    start_time_ec = time.time()

    num_cores_to_use = multiprocessing.cpu_count()
    # Ensure num_cores_to_use is at least 1, even if cpu_count() returns None or 0
    if num_cores_to_use is None or num_cores_to_use < 1:
        num_cores_to_use = 1
    print(f"  Using {num_cores_to_use} cores for parallel Ec calculation.")

    results_G_times_w = [] # List to store results from pool
    # Use with statement for proper pool management
    # starmap unpacks arguments from task_args for process_grid_point
    # chunksize can be tuned for performance with very many tasks
    chunk_size = max(1, n_grid_points // (num_cores_to_use * 4)) if num_cores_to_use > 0 else n_grid_points

    with multiprocessing.Pool(processes=num_cores_to_use) as pool:
        results_G_times_w = pool.starmap(process_grid_point, task_args, chunksize=chunk_size)

    end_time_ec = time.time()
    print(f"  Parallel Ec calculation finished in {end_time_ec - start_time_ec:.2f} seconds.")

    # Summing up all [G_AB(D(r_k)) * w_k] matrices from the parallel tasks
    Ec_matrix = numpy.sum(results_G_times_w, axis=0)

    print("  Correlation Energy Matrix Ec_matrix (Hartree):\n", Ec_matrix)

    # 5. (V_ext is already included in H_CASSCF_matrix as part of SA-CASSCF energies)

    # 6. Assemble Full MSDFT Hamiltonian H_msdft
    # H_MSDFT_AB = H_CASSCF_AB + Ec_AB
    H_msdft_matrix = H_CASSCF_matrix + Ec_matrix
    print("  MSDFT Hamiltonian H_msdft_matrix (Hartree):\n", H_msdft_matrix)

    # 7. Solve Eigenvalue Problem for H_msdft (S is identity, so it's a standard eigh problem)
    msdft_energies, msdft_coeffs = scipy.linalg.eigh(H_msdft_matrix)

    print(f"  MSDFT-LYP Energies: S0={msdft_energies[0]:.8f}, S1={msdft_energies[1]:.8f} Hartree")

    # Print MSDFT eigenvectors (coefficients)
    print("\n--- MSDFT Eigenvectors ---")
    print("Eigenvectors (columns):")
    print(msdft_coeffs)

    # Verify diagonalization of H_msdft_matrix
    print("\nVerification of MSDFT Hamiltonian diagonalization:")
    diag_H = numpy.dot(msdft_coeffs.T, numpy.dot(H_msdft_matrix, msdft_coeffs))
    print("Diagonalized H_msdft matrix:")
    print(diag_H)

    print(f"--- MSDFT-LYP for R_HH = {R_HH_current_distance:.3f} A Finished ---")

    return msdft_energies[0], msdft_energies[1]


# --- Main Script to Calculate Dissociation Curve ---
if __name__ == '__main__':
    # For matrix printing test, just use a single point
    # Use equilibrium bond length of H2 (0.74 Å)
    R_HH_points = numpy.array([0.74])

    # Uncomment the following sections to run the full dissociation curve
    """
    # Define H-H distances for the dissociation curve with finer spacing
    # From 0.5 to 1.0 Å with 0.02 Å spacing
    R_HH_points_fine = numpy.arange(0.5, 1.02, 0.02)

    # From 1.0 to 2.0 Å with 0.05 Å spacing
    R_HH_points_medium = numpy.arange(1.05, 2.05, 0.05)

    # From 2.0 to 4.0 Å with 0.2 Å spacing for dissociation limit
    R_HH_points_coarse = numpy.arange(2.2, 4.2, 0.2)

    # Combine all points
    R_HH_points = numpy.concatenate([R_HH_points_fine, R_HH_points_medium, R_HH_points_coarse])
    """

    print(f"Starting H2 dissociation curve calculation for {len(R_HH_points)} points.")

    energies_S0_msdft = []
    energies_S1_msdft = []

    energies_S0_casscf = [] # For comparison
    energies_S1_casscf = [] # For comparison

    energies_S0_fci = [] # For Full CI reference
    energies_S1_fci = [] # For Full CI reference

    total_start_time = time.time()

    for i_scan_idx, R_val_scan in enumerate(R_HH_points):
        print(f"\nProcessing R_HH = {R_val_scan:.3f} A ({i_scan_idx+1}/{len(R_HH_points)})...")

        # Create molecule, RHF, and CASSCF objects for the current geometry
        # This ensures a fresh start for each point, important for CASSCF state tracking/convergence.
        mol_scan = gto.Mole()
        mol_scan.atom = [['H', (0,0,0)], ['H', (0,0,R_val_scan)]]
        mol_scan.basis='cc-pVTZ'
        mol_scan.symmetry=False # Keep it simple
        mol_scan.build()

        mf_scan = scf.RHF(mol_scan)
        mf_scan.kernel(verbose=lib.logger.QUIET) # Keep RHF quiet

        mc_scan = mcscf.CASSCF(mf_scan, 2, (1,1)) # ncas=2, nelecas=(1alpha,1beta)
        mc_scan.state_average_([0.5,0.5]) # Equal weights for 2 states
        mc_scan.nroots = N_STATES

        # Corrected verbose setting for mc_scan.kernel()
        if R_val_scan > 0.65: # Slightly adjust threshold for quiet CASSCF
            mc_scan.verbose = lib.logger.WARNING
        else:
            mc_scan.verbose = lib.logger.INFO # Print CASSCF convergence for smaller R

        mc_scan.kernel() # Run SA-CASSCF

        # Store FCI energies
        # We need to run FCI calculation here to ensure it's done for each geometry
        cisolver_scan = fci.FCI(mf_scan)
        cisolver_scan.nroots = N_STATES
        fci_scan_e, _ = cisolver_scan.kernel(nroots=N_STATES)
        fci_scan_S0_energy = fci_scan_e[0] + mol_scan.energy_nuc()
        fci_scan_S1_energy = fci_scan_e[1] + mol_scan.energy_nuc()

        # Store FCI energies
        energies_S0_fci.append(fci_scan_S0_energy)
        energies_S1_fci.append(fci_scan_S1_energy)

        if not mc_scan.converged:
            print(f"SA-CASSCF FAILED to converge for R_HH = {R_val_scan:.3f} A. Skipping MSDFT for this point.")
            energies_S0_casscf.append(float('nan'))
            energies_S1_casscf.append(float('nan'))
            energies_S0_msdft.append(float('nan'))
            energies_S1_msdft.append(float('nan'))
            continue # Move to the next R value

        energies_S0_casscf.append(mc_scan.e_states[0])
        energies_S1_casscf.append(mc_scan.e_states[1])

        # Pass the converged mc_scan object to the MSDFT calculation
        E0_msdft_val, E1_msdft_val = calculate_msdft_lyp_single_point(
            R_val_scan,
            mol_obj=mol_scan,
            mf_rhf=mf_scan, # mf_scan is not strictly used by MSDFT part if mc_scan is passed
            mc_sacasscf_obj=mc_scan
        )

        energies_S0_msdft.append(E0_msdft_val)
        energies_S1_msdft.append(E1_msdft_val)

    total_end_time = time.time()
    print(f"\nTotal dissociation curve calculation time: {total_end_time - total_start_time:.2f} seconds.")

    # --- Output Results Table ---
    print("\n--- Dissociation Curve Results (Hartree) ---")
    header = "R_HH (A) | E_S0 (FCI) | E_S1 (FCI) | E_S0 (CASSCF) | E_S1 (CASSCF) | E_S0 (MSDFT-LYP) | E_S1 (MSDFT-LYP)"
    print(header)
    print("-" * len(header))
    for i_res, R_res_val in enumerate(R_HH_points):
        print(f"{R_res_val:8.3f} | "
              f"{energies_S0_fci[i_res]:10.8f} | "
              f"{energies_S1_fci[i_res]:10.8f} | "
              f"{energies_S0_casscf[i_res]:13.8f} | "
              f"{energies_S1_casscf[i_res]:13.8f} | "
              f"{energies_S0_msdft[i_res]:16.8f} | "
              f"{energies_S1_msdft[i_res]:16.8f}")

    # --- Plotting Results ---
    plt.figure(figsize=(14, 8)) # Larger figure for more data

    # Plot FCI results with solid lines
    plt.plot(R_HH_points, energies_S0_fci, 'g-', label='S0 (FCI)', linewidth=3, markersize=0)
    plt.plot(R_HH_points, energies_S1_fci, 'm-', label='S1 (FCI)', linewidth=3, markersize=0)

    # Plot CASSCF results with markers and lines
    plt.plot(R_HH_points, energies_S0_casscf, 'bo-', label='S0 (SA-CASSCF)', linewidth=2, markersize=6)
    plt.plot(R_HH_points, energies_S1_casscf, 'ro-', label='S1 (SA-CASSCF)', linewidth=2, markersize=6)

    # Plot MSDFT results with different markers and dashed lines
    plt.plot(R_HH_points, energies_S0_msdft, 'b*--', label='S0 (MSDFT-LYP)', linewidth=2, markersize=8)
    plt.plot(R_HH_points, energies_S1_msdft, 'r*--', label='S1 (MSDFT-LYP)', linewidth=2, markersize=8)

    plt.xlabel('H-H Distance (Angstrom)', fontsize=14)
    plt.ylabel('Energy (Hartree)', fontsize=14)
    plt.title('H$_2$ Dissociation Curve: FCI vs SA-CASSCF(2,2) vs MSDFT-LYP', fontsize=16)
    plt.legend(fontsize=12, loc='best')
    plt.grid(True, linestyle=':', alpha=0.7)

    # Adjust y-limits to ensure all data is visible, handling potential NaNs
    # Convert lists to numpy arrays for nanmin/nanmax if they aren't already
    e0_fci_np = numpy.array(energies_S0_fci)
    e1_fci_np = numpy.array(energies_S1_fci)
    e0_cas_np = numpy.array(energies_S0_casscf)
    e1_cas_np = numpy.array(energies_S1_casscf)
    e0_ms_np = numpy.array(energies_S0_msdft)
    e1_ms_np = numpy.array(energies_S1_msdft)

    valid_energies_for_plot = numpy.concatenate([
        e0_fci_np[numpy.isfinite(e0_fci_np)],
        e1_fci_np[numpy.isfinite(e1_fci_np)],
        e0_cas_np[numpy.isfinite(e0_cas_np)],
        e1_cas_np[numpy.isfinite(e1_cas_np)],
        e0_ms_np[numpy.isfinite(e0_ms_np)],
        e1_ms_np[numpy.isfinite(e1_ms_np)]
    ])

    if len(valid_energies_for_plot) > 0:
        min_e = numpy.min(valid_energies_for_plot)
        max_e = numpy.max(valid_energies_for_plot)
        padding = (max_e - min_e) * 0.05 # 5% padding
        if padding < 0.05: padding = 0.05 # Minimum padding
        plt.ylim(min_e - padding, max_e + padding * 2) # More space at top

    plt.tight_layout() # Adjust plot to prevent labels from being cut off
    plt.savefig("h2_dissociation_fci_casscf_msdft_lyp.png", dpi=300) # Higher DPI for better quality
    print("\nPlot saved to h2_dissociation_fci_casscf_msdft_lyp.png")
    # plt.show() # Uncomment to display plot interactively if not running in a headless environment
