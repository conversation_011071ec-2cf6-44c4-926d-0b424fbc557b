#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy
from pyscf import gto, scf, dft, lib
import scipy.linalg
from scipy.spatial import KDTree
import multiprocessing
import time
import matplotlib.pyplot as plt

# --- Global Constants for LYP (from physrevb.37.785) ---
LYP_A = 0.04918
LYP_B = 0.132
LYP_C = 0.2533
LYP_D = 0.349
C_F = (3.0/10.0) * (3.0 * numpy.pi**2)**(2.0/3.0)

def get_density_derivatives(rho_values_at_k, k_index, all_rho_values_on_grid, grid_coords, kdtree, h_fd=0.01):
    """
    Calculate gradient and Laplacian of density at grid point k using finite differences.
    """
    grad_rho = numpy.zeros(3)
    laplacian_rho = 0.0
    current_pos = grid_coords[k_index]

    # Gradient (central difference)
    for i_dim in range(3): # x, y, z
        # Find neighbor in +h direction
        pos_plus_h = current_pos.copy()
        pos_plus_h[i_dim] += h_fd
        _, idx_plus = kdtree.query(pos_plus_h, k=1)
        rho_plus_h = all_rho_values_on_grid[idx_plus]
        actual_h_plus = grid_coords[idx_plus][i_dim] - current_pos[i_dim]
        if abs(actual_h_plus) < 1e-7: actual_h_plus = h_fd

        # Find neighbor in -h direction
        pos_minus_h = current_pos.copy()
        pos_minus_h[i_dim] -= h_fd
        _, idx_minus = kdtree.query(pos_minus_h, k=1)
        rho_minus_h = all_rho_values_on_grid[idx_minus]
        actual_h_minus = current_pos[i_dim] - grid_coords[idx_minus][i_dim]
        if abs(actual_h_minus) < 1e-7: actual_h_minus = h_fd

        if abs(actual_h_plus + actual_h_minus) < 1e-7:
             grad_rho[i_dim] = 0.0
        else:
             grad_rho[i_dim] = (rho_plus_h - rho_minus_h) / (actual_h_plus + actual_h_minus)

    # Laplacian (sum of second derivatives)
    for i_dim in range(3):
        pos_plus_h = current_pos.copy(); pos_plus_h[i_dim] += h_fd
        _, idx_plus = kdtree.query(pos_plus_h, k=1)
        rho_plus_h = all_rho_values_on_grid[idx_plus]
        h2 = grid_coords[idx_plus][i_dim] - current_pos[i_dim]
        if abs(h2) < 1e-7: h2 = h_fd

        pos_minus_h = current_pos.copy(); pos_minus_h[i_dim] -= h_fd
        _, idx_minus = kdtree.query(pos_minus_h, k=1)
        rho_minus_h = all_rho_values_on_grid[idx_minus]
        h1 = current_pos[i_dim] - grid_coords[idx_minus][i_dim]
        if abs(h1) < 1e-7: h1 = h_fd

        denominator_lap = h1 * h2 * (h1 + h2)
        if abs(denominator_lap) > 1e-12:
            numerator_lap = h1 * rho_plus_h - (h1 + h2) * rho_values_at_k + h2 * rho_minus_h
            laplacian_rho += 2 * numerator_lap / denominator_lap

    return grad_rho, laplacian_rho

def calculate_gLYP_scalar(rho_val, grad_rho_vec, laplacian_rho_val):
    """Calculates g_LYP for a given density and its derivatives."""
    if rho_val < 1e-12: # Threshold for density being too small
        return 0.0

    grad_rho_sq = numpy.dot(grad_rho_vec, grad_rho_vec)

    # Weizsacker kinetic energy density t_W(rho)
    # t_W = (1/8) * |nabla rho|^2 / rho - (1/8) * nabla^2 rho
    t_W_rho = 0.0
    if rho_val > 1e-9:
        t_W_rho = (1.0/8.0) * (grad_rho_sq / rho_val) - (1.0/8.0) * laplacian_rho_val
    elif abs(rho_val) <= 1e-9 and abs(grad_rho_sq) > 1e-9:
        t_W_rho = - (1.0/8.0) * laplacian_rho_val
    else:
        t_W_rho = - (1.0/8.0) * laplacian_rho_val

    # LYP formula terms (from physrevb.37.785, Eq. 21)
    # g_LYP = -a * [rho + b*rho^(-2/3)*exp(-c*rho^(-1/3))*(CF*rho^(5/3) - (17/9)*t_W + (1/18)*nabla^2_rho)] / (1+d*rho^(-1/3))

    if rho_val < 1e-9:
        return 0.0

    rho_m13 = rho_val**(-1.0/3.0)
    rho_m23 = rho_m13**2
    rho_53  = rho_val**(5.0/3.0)

    term_CF_rho_53 = C_F * rho_53
    term_tW_combo = -(17.0/9.0) * t_W_rho
    term_laplacian_direct = (1.0/18.0) * laplacian_rho_val

    inner_bracket = term_CF_rho_53 + term_tW_combo + term_laplacian_direct

    exp_term = numpy.exp(-LYP_C * rho_m13)

    numerator = rho_val + LYP_B * rho_m23 * inner_bracket * exp_term
    denominator = 1.0 + LYP_D * rho_m13

    if abs(denominator) < 1e-12:
        return 0.0

    return -LYP_A * (numerator / denominator)

def process_grid_point_single_state(k_grid_point_idx, rho_at_k, all_rho_values_on_grid, 
                                   grid_coords_all_points, kdtree_all_points, grid_weight_at_k):
    """
    Processes a single grid point to calculate its contribution to LYP correlation energy.
    Returns g_LYP(rho(r_k)) * w_k
    """
    
    # Calculate numerical derivatives of density
    grad_rho_rk, laplacian_rho_rk = get_density_derivatives(
        rho_at_k, k_grid_point_idx, all_rho_values_on_grid,
        grid_coords_all_points, kdtree_all_points
    )
    
    # Calculate g_LYP(rho(r_k))
    g_val_rho_rk = calculate_gLYP_scalar(rho_at_k, grad_rho_rk, laplacian_rho_rk)
    
    # Return contribution weighted by grid weight
    return g_val_rho_rk * grid_weight_at_k

def calculate_h2_single_state_lyp(R_HH_distance):
    """Calculate H2 ground state energy with LYP correlation at given bond distance."""
    
    print(f"\n--- Calculating H2 ground state at R_HH = {R_HH_distance:.3f} A ---")
    
    # 1. Define molecule and perform RHF
    mol = gto.Mole()
    mol.atom = [['H', (0, 0, 0)], ['H', (0, 0, R_HH_distance)]]
    mol.basis = 'cc-pVTZ'
    mol.symmetry = False
    mol.build()
    
    mf = scf.RHF(mol)
    mf.kernel(verbose=lib.logger.QUIET)
    
    if not mf.converged:
        print(f"RHF calculation did not converge for R_HH = {R_HH_distance:.3f} A.")
        return float('nan')
    
    E_HF = mf.e_tot
    print(f"  RHF Energy: {E_HF:.8f} Hartree")
    
    # 2. Get density matrix
    dm_ao = mf.make_rdm1()
    
    # 3. Setup DFT Grids
    grids = dft.gen_grid.Grids(mol)
    grids.atom_grid = (99, 590)
    grids.radi_method = dft.radi.treutler
    grids.becke_scheme = dft.gen_grid.original_becke
    grids.build(with_non0tab=True)
    grid_coords = grids.coords
    grid_weights = grids.weights
    n_grid_points = grid_coords.shape[0]
    print(f"  Number of DFT grid points: {n_grid_points}")
    
    # 4. Calculate density on grid
    ni = dft.numint.NumInt()
    ao_values = ni.eval_ao(mol, grid_coords, deriv=0)
    
    rho_on_grid = numpy.zeros(n_grid_points)
    for i in range(n_grid_points):
        ao_i = ao_values[i]
        rho_on_grid[i] = numpy.dot(ao_i, numpy.dot(dm_ao, ao_i))
    
    print("  Finished calculating density on grid.")
    
    # 5. Build KDTree for numerical differentiation
    kdtree_grid = KDTree(grid_coords)
    
    # 6. Calculate LYP correlation energy (parallelized)
    task_args = []
    for k_idx in range(n_grid_points):
        task_args.append((
            k_idx, rho_on_grid[k_idx], rho_on_grid,
            grid_coords, kdtree_grid, grid_weights[k_idx]
        ))
    
    print(f"  Starting parallel computation of LYP correlation over {n_grid_points} grid points...")
    start_time = time.time()
    
    num_cores = multiprocessing.cpu_count()
    if num_cores is None or num_cores < 1:
        num_cores = 1
    print(f"  Using {num_cores} cores for parallel calculation.")
    
    chunk_size = max(1, n_grid_points // (num_cores * 4))
    
    with multiprocessing.Pool(processes=num_cores) as pool:
        results = pool.starmap(process_grid_point_single_state, task_args, chunksize=chunk_size)
    
    end_time = time.time()
    print(f"  Parallel calculation finished in {end_time - start_time:.2f} seconds.")
    
    # 7. Sum up correlation energy
    E_c_LYP = numpy.sum(results)
    print(f"  LYP Correlation Energy: {E_c_LYP:.8f} Hartree")
    
    # 8. Total energy
    E_total = E_HF + E_c_LYP
    print(f"  Total Energy (RHF+LYP): {E_total:.8f} Hartree")
    print(f"--- Finished R_HH = {R_HH_distance:.3f} A ---")
    
    return E_total, E_HF, E_c_LYP

if __name__ == '__main__':
    # Define H-H distances for the dissociation curve
    R_HH_points = numpy.arange(0.5, 4.1, 0.1)

    print(f"Starting H2 ground state dissociation curve calculation for {len(R_HH_points)} points.")

    energies_total = []
    energies_hf = []
    energies_corr = []

    total_start_time = time.time()

    for i, R_val in enumerate(R_HH_points):
        print(f"\nProcessing R_HH = {R_val:.3f} A ({i+1}/{len(R_HH_points)})...")

        try:
            E_total, E_HF, E_c = calculate_h2_single_state_lyp(R_val)
            energies_total.append(E_total)
            energies_hf.append(E_HF)
            energies_corr.append(E_c)
        except Exception as e:
            print(f"Error at R_HH = {R_val:.3f} A: {e}")
            energies_total.append(float('nan'))
            energies_hf.append(float('nan'))
            energies_corr.append(float('nan'))

    total_end_time = time.time()
    print(f"\nTotal calculation time: {total_end_time - total_start_time:.2f} seconds.")

    # Output results table
    print("\n--- H2 Ground State Dissociation Curve Results (Hartree) ---")
    header = "R_HH (A) | E_HF | E_c (LYP) | E_total (RHF+LYP)"
    print(header)
    print("-" * len(header))

    for i, r in enumerate(R_HH_points):
        print(f"{r:8.3f} | {energies_hf[i]:12.8f} | {energies_corr[i]:12.8f} | {energies_total[i]:12.8f}")

    # Plot results
    plt.figure(figsize=(12, 8))

    valid_indices = [i for i, e in enumerate(energies_total) if not numpy.isnan(e)]
    R_valid = [R_HH_points[i] for i in valid_indices]
    E_total_valid = [energies_total[i] for i in valid_indices]
    E_hf_valid = [energies_hf[i] for i in valid_indices]

    plt.plot(R_valid, E_hf_valid, 'b-o', label='RHF', linewidth=2, markersize=4)
    plt.plot(R_valid, E_total_valid, 'r-s', label='RHF+LYP', linewidth=2, markersize=4)

    plt.xlabel('H-H Distance (Angstrom)', fontsize=14)
    plt.ylabel('Energy (Hartree)', fontsize=14)
    plt.title('H$_2$ Ground State Dissociation Curve: RHF vs RHF+LYP', fontsize=16)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle=':', alpha=0.7)

    plt.tight_layout()
    plt.savefig("h2_ground_state_lyp.png", dpi=300)
    print("\nPlot saved to h2_ground_state_lyp.png")
